import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/orders/model/order_type.dart';
import 'package:zen_spider/zen_spider.dart';

class OrderAdapterService {
  static List<OrderFormModel> convertIndexInvestmentsToOrders({
    required List<IndexInvestment> indexInvestments,
    required int clientId,
    required int accountId,
    required int strategyId,
    required String broker,
    required String exchange,
    required String transactionType, // "BUY" or "SELL"
    String product = "CNC",
    String validity = "DAY",
    String methodType = "POST",
  }) {
    return indexInvestments.map((investment) {
      return OrderFormModel(
        clientId: clientId,
        accountId: accountId,
        strategyId: strategyId,
        broker: broker,
        exchange: exchange,
        transactionType: transactionType,
        quantity: investment.quantity,
        product: product,
        validity: validity,
        methodType: methodType,
        zenId: investment.zenId,
        isMarket: investment.isMarket,
        orderType: OrderType.MARKET,
        // optional / nullable fields:
        minutes: null,
        stopLossLimitPrice: null,
        triggerPrice: null,
        limitPrice: null,
        activationPoints: null,
        trailingTriggerPoints: null,
        trailingLimitPoints: null,
        isStoplossEnabled: null,
        isTrailingStoplossEnabled: null,
        stoplossType: null,
        zenOrderId: null,
      );
    }).toList();
  }
}
