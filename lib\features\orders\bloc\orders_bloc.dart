import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/common/position_comp_key.dart';
import 'package:phoenix/features/orders/data/repository/order_form_repository.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/orders/model/order_response_model.dart';

part 'orders_event.dart';
part 'orders_state.dart';

class OrdersBloc extends Bloc<OrdersEvent, OrdersState> {
  final OrderFormRepository _repository;

  OrdersBloc(this._repository) : super(OrdersInitial()) {
    on<PlaceOrderEvent>(_placeOrderEvent);
    on<OrdersInitializeEvent>(_ordersInitializeEvent);
    on<PlaceBulkOrderEvent>(_placeBulkOrderEvent);
  }

  void _placeOrderEvent(PlaceOrderEvent event, Emitter<OrdersState> emit) async {
    emit(OrderLoading());
    try{
      OrderResponseModel response = await _repository.placeOrder(event.data);
      emit(OrderPlaced(status: response.status,message: response.message, tradingSymbol: response.tradingSymbol, methodType: response.methodType, ));
    } catch (e) {
      debugPrint("3. catch and emit to ui $e");
      emit(OrderError(e.toString()));
    }
   
  }

  void _placeBulkOrderEvent(PlaceBulkOrderEvent event, Emitter<OrdersState> emit) async {
  final total = event.data.length;
  emit(BulkOrderProcessing(total: total, processed: 0));

  List<OrderResponseModel> responses = List.filled(total, OrderResponseModel(status: "PENDING", message: "", tradingSymbol: "", methodType: "", zenOrderId: 0, positionCompKey: PositionCompKey(zenSecId: 0, clientId: 0, broker: "", accountId: 0, strategyId: 0)));
  int accepted = 0;
  int rejected = 0;

  // Create a list of Futures
  final futures = List.generate(event.data.length, (index) async {
    final orderData = event.data[index];
    try {
      final response = await _repository.placeOrder(orderData);
      responses[index] = response;
      if (response.status == "ACCEPTED") {
        accepted++;
      } else {
        rejected++;
      }
    } catch (e) {
      debugPrint("Error during bulk order processing: $e");
      rejected++;
      responses[index] = OrderResponseModel(status: "REJECTED", message: e.toString(), tradingSymbol: orderData.zenId.toString(), methodType: orderData.methodType, zenOrderId: 0, positionCompKey: PositionCompKey(zenSecId: orderData.zenId, clientId: orderData.clientId, broker: orderData.broker, accountId: orderData.accountId, strategyId: orderData.strategyId));
    }
    // You can emit intermediate state updates here if needed
    //emit(BulkOrderProcessing(total: total, processed: index + 1));
  });

  // Wait for all orders to complete
  await Future.wait(futures);

  // Emit completion state
  emit(BulkOrderCompleted(
    total: total,
    accepted: accepted,
    rejected: rejected,
    responses: responses,
  ));
}


  



  //This is to reset the bloc state after cehcking the orders state in ui
  //
  void _ordersInitializeEvent(OrdersInitializeEvent event, Emitter<OrdersState> emit) {
    debugPrint(" 📦 Orders bloc state set to Initial");
    emit(OrdersInitial());

  }

}

