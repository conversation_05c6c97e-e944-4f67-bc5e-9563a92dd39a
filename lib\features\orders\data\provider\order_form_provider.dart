import 'dart:convert';
import 'package:phoenix/features/common/api_response_model.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class OrderFormProvider {
  
  Future<Map<String, dynamic>> placeOrder(OrderFormModel orderData) async {
    final customHttpService = HttpService();

      // Define the request body
      final requestBody = orderData.toJson();

      // Make the POST request
      final response = await customHttpService.post(
        Uri.parse(ApiPath.placeOrder(orderData.clientId)),
        body: json.encode(requestBody),
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if(apiResponse.code != 200 || apiResponse.status != 'SUCCESS' || response.statusCode != 200){
        throw AppException(apiResponse.message);
      } else {
        // Decode the JSON response into a list of dynamic objects
        final jsonData =  apiResponse.payload as Map<String, dynamic>;
        return jsonData;
      }
  }
  
}
