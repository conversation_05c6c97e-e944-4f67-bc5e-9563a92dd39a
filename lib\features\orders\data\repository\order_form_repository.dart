
import 'package:phoenix/features/orders/data/provider/order_form_provider.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/orders/model/order_response_model.dart';


class OrderFormRepository {
  final OrderFormProvider provider;

  OrderFormRepository(this.provider);

  Future<OrderResponseModel> placeOrder(OrderFormModel orderData) async {
    try {
      // Use the provider to fetch data for eqHedge
      final responseData = await provider.placeOrder(orderData);
      return OrderResponseModel.fromJson(responseData);

    } catch (e) {
      print("2. OrderFormRepository chatch and throw");
      throw Exception(e);
    }
  }
}
