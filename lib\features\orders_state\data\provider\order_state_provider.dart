import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/managers/api/api_response_manager.dart';
import 'package:phoenix/services/request_body_filter_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class OrderStateProvider {
  final customHttpService = HttpService();
  Future<List<UnifiedOrderData>> getUnifiedOrderState(int clientId) async {
    try {
      final positionCompKeyFilter =
          await RequestBodyFilterService.buildPositionCompKeyFilter(clientId);

      final requestBody = jsonEncode({
        ...positionCompKeyFilter,
      });

      // Make the POST request
      final response = await customHttpService.post(
        Uri.parse(ApiPath.getUnifiedOrderState(clientId)),
        body: requestBody,
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code != 200 ||
          apiResponse.status != 'SUCCESS' ||
          response.statusCode != 200) {
        throw AppException(apiResponse.message);
      } else {
        // Decode the JSON response into a list of dynamic objects
        final jsonData = apiResponse.payload as List<dynamic>;
        // Map each dynamic object to a ZenOrderStateModel
        return jsonData.map((data) => UnifiedOrderData.fromJson(data)).toList();
      }
    } catch (e) {
      // Handle any errors that may occur
      debugPrint('Error fetching UnifiedOrderState: $e');

      throw e is AppException
          ? e
          : AppException('Failed to fetch Unified Order State: ${e.toString()}');
    }
  }
}
