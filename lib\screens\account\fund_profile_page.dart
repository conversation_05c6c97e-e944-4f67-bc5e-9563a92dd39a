import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/features/margin_state/bloc/margin_state_bloc.dart';
import 'package:phoenix/features/margin_state/model/margin_data.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/screens/account/widgets/client_selection_widget.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/circular_loader.dart';

class FundProfilePage extends StatefulWidget {
  const FundProfilePage({super.key});

  @override
  State<FundProfilePage> createState() => _FundProfilePageState();
}

class _FundProfilePageState extends State<FundProfilePage> {
  @override
  void initState() {
    super.initState();
    // Fetch margin data when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchMarginData();
    });
  }

  void _fetchMarginData() {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      final user = authState.credentialsModel;
      List<int> accountIds = [];
      
      // Collect all account IDs from all brokers
      for (var broker in user.brokers) {
        for (var account in broker.accounts) {
          accountIds.add(account.accountId);
        }
      }
      
      if (accountIds.isNotEmpty) {
        context.read<MarginBloc>().add(
          MarginFetchMultipleEvent(user.clientId, accountIds),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthUnauthenticated) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            '/',
            (Route<dynamic> route) => false,
          ); // remove all previous routes);
        }
      },
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) {
              return Scaffold(
                backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
                body: const Center(child: CircularLoader()),
              );
            },
          );
        }
        final user = state.credentialsModel;

        return BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themeState) {
            return Scaffold(
              backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
              appBar: AppBar(
                backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
                elevation: 0,
                title: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.person,
                        color: Colors.blue,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      "Fund Profile",
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                iconTheme: IconThemeData(color: AppTheme.textPrimary(themeState.isDarkMode)),
              ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header Card with Margin Summary
                  BlocBuilder<MarginBloc, MarginState>(
                    builder: (context, marginState) {
                      return _buildProfileCard(user, themeState, marginState);
                    },
                  ),
                  const SizedBox(height: 16),

                  // Client Selection Widget
                  ClientSelectionWidget(userClientId: user.clientId),
                  const SizedBox(height: 16),

                  // Overview Cards Row
                  _buildOverviewCards(user, themeState),
                  const SizedBox(height: 16),

                  // Accounts Section with Margin Data
                  BlocBuilder<MarginBloc, MarginState>(
                    builder: (context, marginState) {
                      return _buildAccountsSection(user, themeState, marginState);
                    },
                  ),
                  const SizedBox(height: 16),

                  // Strategies Section
                  _buildStrategiesSection(user, themeState),
                ],
              ),
            ),
          ),
            );
          },
        );
      },
    );
  }

  Widget _buildProfileCard(CredentialsModel user, ThemeState themeState, MarginState marginState) {
    // Calculate total available cash
    double totalCash = 0.0;
    if (marginState is MarginMultipleLoaded) {
      for (var marginData in marginState.margins.values) {
        totalCash += marginData.availableCash;
      }
    }
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode 
                ? Colors.black.withOpacity(0.2) 
                : Colors.grey.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundImage: NetworkImage(user.pictureUrl),
                backgroundColor: Colors.grey[300],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hi, ${user.name.split("@").first.toCapitalized}',
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      user.email,
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              // Total Available Cash in header
              if (marginState is MarginMultipleLoaded && totalCash > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.green.withOpacity(0.15),
                        Colors.green.withOpacity(0.08),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Available Cash',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '₹${_formatCurrency(totalCash)}',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                )
              else if (marginState is MarginLoading)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: themeState.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.textSecondary(themeState.isDarkMode),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          // Compact info grid
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: themeState.isDarkMode ? Colors.grey[800]?.withOpacity(0.5) : Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(child: _buildCompactInfoItem('Client ID', user.clientId.toString(), themeState)),
                    const SizedBox(width: 12),
                    Expanded(child: _buildCompactInfoItem('User ID', user.userId, themeState)),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(child: _buildCompactInfoItem('Client Name', user.clientName, themeState)),
                    const SizedBox(width: 12),
                    Expanded(child: _buildCompactInfoItem('Zen User ID', user.zenUserId, themeState)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactInfoItem(String label, String value, ThemeState themeState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textSecondary(themeState.isDarkMode),
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            color: AppTheme.textPrimary(themeState.isDarkMode),
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildOverviewCards(CredentialsModel user, ThemeState themeState) {
    List<AccountInfo> allAccounts = [];
    for (var broker in user.brokers) {
      allAccounts.addAll(broker.accounts);
    }

    List<Strategy> allStrategies = [];
    for (var broker in user.brokers) {
      for (var account in broker.accounts) {
        allStrategies.addAll(account.strategies);
      }
    }

    return Row(
      children: [
        Expanded(
          child: _buildOverviewCard(
            'Brokers',
            user.brokers.length.toString(),
            Icons.account_balance,
            Colors.blue,
            themeState,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Accounts',
            allAccounts.length.toString(),
            Icons.account_circle,
            Colors.orange,
            themeState,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Strategies',
            allStrategies.length.toString(),
            Icons.trending_up,
            Colors.teal,
            themeState,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCard(String title, String count, IconData icon, Color color, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode 
                ? Colors.black.withOpacity(0.1) 
                : Colors.grey.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            count,
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildAccountsSection(CredentialsModel user, ThemeState themeState, MarginState marginState) {
    List<AccountInfo> allAccounts = [];
    for (var broker in user.brokers) {
      allAccounts.addAll(broker.accounts);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.account_circle,
              color: AppTheme.textPrimary(themeState.isDarkMode),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Trading Accounts',
              style: TextStyle(
                color: AppTheme.textPrimary(themeState.isDarkMode),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${allAccounts.length} accounts',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...user.brokers.map((broker) => 
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (broker.accounts.isNotEmpty) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.2)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.account_balance,
                        color: Colors.blue,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        broker.brokerName,
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        'ID: ${broker.brokerId}',
                        style: TextStyle(
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                ...broker.accounts.map((account) => _buildAccountCard(account, themeState, marginState)),
                const SizedBox(height: 12),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAccountCard(AccountInfo account, ThemeState themeState, MarginState marginState) {
    // Get margin data for this account
    MarginData? marginData;
    if (marginState is MarginMultipleLoaded) {
      marginData = marginState.margins[account.accountId];
    }
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode 
                ? Colors.black.withOpacity(0.1) 
                : Colors.grey.withOpacity(0.04),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.account_circle,
                  color: Colors.orange,
                  size: 16,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      account.accountName,
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'ID: ${account.accountId}',
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              // Margin data in the header
              if (marginData != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        color: Colors.green,
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '₹${_formatCurrency(marginData.availableCash)}',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                )
              else if (marginState is MarginLoading)
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: themeState.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 1.5,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.textSecondary(themeState.isDarkMode),
                      ),
                    ),
                  ),
                )
              else if (marginState is MarginError)
                GestureDetector(
                  onTap: _fetchMarginData,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.refresh,
                      color: Colors.red,
                      size: 12,
                    ),
                  ),
                ),
            ],
          ),
          // Strategies count
          if (account.strategies.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.05),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    color: Colors.purple,
                    size: 12,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    '${account.strategies.length} active strategies',
                    style: TextStyle(
                      color: Colors.purple,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStrategiesSection(CredentialsModel user, ThemeState themeState) {
    List<Strategy> allStrategies = [];
    for (var broker in user.brokers) {
      for (var account in broker.accounts) {
        allStrategies.addAll(account.strategies);
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.trending_up,
              color: AppTheme.textPrimary(themeState.isDarkMode),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Active Strategies',
              style: TextStyle(
                color: AppTheme.textPrimary(themeState.isDarkMode),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.teal.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${allStrategies.length} strategies',
                style: TextStyle(
                  color: Colors.teal,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (allStrategies.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: themeState.isDarkMode ? Colors.grey[850] : Colors.grey[50],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                  size: 24,
                ),
                const SizedBox(height: 8),
                Text(
                  'No strategies available',
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          )
        else
          ...user.brokers.map((broker) => 
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...broker.accounts.map((account) => 
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (account.strategies.isNotEmpty) ...[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                          margin: const EdgeInsets.only(bottom: 8),
                          decoration: BoxDecoration(
                            color: Colors.teal.withOpacity(0.05),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.teal.withOpacity(0.2)),
                          ),
                          child: Text(
                            '${broker.brokerName} - ${account.accountName}',
                            style: TextStyle(
                              color: Colors.teal.shade700,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        ...account.strategies.map((strategy) => _buildStrategyCard(strategy, themeState)),
                        const SizedBox(height: 12),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildStrategyCard(Strategy strategy, ThemeState themeState) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode 
                ? Colors.black.withOpacity(0.1) 
                : Colors.grey.withOpacity(0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.teal.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.trending_up,
              color: Colors.teal,
              size: 16,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  strategy.strategyName,
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'ID: ${strategy.strategyId}',
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: AppTheme.textSecondary(themeState.isDarkMode),
            size: 14,
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(2)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(2)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(2)}K';
    } else {
      return amount.toStringAsFixed(2);
    }
  }
}
