import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:phoenix/adapter/order_adapter_service.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/features/orders/bloc/orders_bloc.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/orders/model/spider_order_data.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/widgets/broker_account_strategy_selector/broker_account_strategy_modal.dart';
import 'package:phoenix/widgets/broker_account_strategy_selector/order_prefrence_model.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';
import 'package:provider/provider.dart';
import 'package:zen_spider/zen_spider.dart';

import '../../features/portfolio_data/bloc/portfolio_bloc.dart';
import '../../features/portfolio_data/data/provider/portfolio_provider.dart';
import '../../features/websocket/bloc/websocket_bloc.dart';
import '../../managers/websocket_manager.dart';
import '../../services/shared_prefrences_service.dart';
import '../../utils/theme_constants.dart';
import '../orders/order_form_bottom_sheet.dart';

class SpiderPage extends StatefulWidget {
  const SpiderPage({super.key});

  @override
  State<SpiderPage> createState() => _SpiderPageState();
}

class _SpiderPageState extends State<SpiderPage> with TickerProviderStateMixin {
  WebSocketManager? _webSocketManager;
  StockRepositoryImpl? _repository;
  late final AnimationController _formSheetAnimeController;
  List<Map<String, dynamic>>? portfolioSpiderData;

  @override
  void initState() {
    super.initState();
    _formSheetAnimeController = BottomSheet.createAnimationController(this);
    _formSheetAnimeController.duration = const Duration(milliseconds: 850);
    final portfolio_data =
        context.read<PortfolioBloc>().state as PortfolioLoaded;
    final positions = portfolio_data.openPositions;
    portfolioSpiderData = positions.map((position) {
      return {
        "zen_id": position.positionCompositeKey.zenSecId,
        "name": position.tradingSymbol,
        "price": position.latestPrice.toDouble(),
        "previousPrice": position.latestPrice.toDouble(),
        "startPrice": position.sodPrice.toDouble(),
        "index": ["Portfolio"],
        "weight": [1.0], // Default weight
      };
    }).toList();
  }

  Future<void> buy(
    BuildContext context,
    String stockName,
    double price,
    int zenId,
  ) async {
    debugPrint('🥸  Buy requested for $stockName at $price');

    try {
      final graphBloc = context.read<GraphBloc>();
      graphBloc.add(
        GraphSellRequested(stockName: stockName, price: price, zenId: zenId),
      );

      if (!context.mounted) return;

      // Show the order form
      await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        transitionAnimationController: _formSheetAnimeController,
        builder: (modalContext) {
          return SingleChildScrollView(
            reverse: true,
            child: OrderFormSheet(
              openOrderType: FormOpenOrderType.spider,
              spiderMetaData: SpiderOrderData(
                zenId: zenId,
                tradingSymbol: stockName,
                transactionType: "BUY",
              ),
            ),
          );
        },
      ).whenComplete(() {
        debugPrint("Spider form closed");
      });

      debugPrint("BUY Order form closed");
      BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));

      // Closing Buy/Sell second slide
      if (Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (!context.mounted) return;

      final ordersBloc = context.read<OrdersBloc>();

      /// ✅ Only proceed if order was actually initiated (i.e., state is OrderLoading)
      if (ordersBloc.state is! OrderLoading) {
        debugPrint("No order initiated for $stockName. Skipping stream wait.");
        return;
      }

      // Show a loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularLoader(),
              SizedBox(width: 16),
              Text("Processing your order..."),
            ],
          ),
        ),
      );

      // Wait for order state to complete
      await for (final orderState in ordersBloc.stream) {
        if (orderState is! OrderLoading) break;
      }

      if (!context.mounted) return;

      // Close loader dialog
      if (Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      final orderState = ordersBloc.state;
      debugPrint("🥸  BUY ${orderState.toString()}");

      if (orderState is OrderPlaced) {
        debugPrint("ORDER STATUS : ${orderState.status}");

        if (orderState.status == "ACCEPTED") {
          ZenSnackbar.show(
            context,
            'Buy order executed for $stockName at ₹${price.toStringAsFixed(2)}',
            type: ToastType.success,
          );
        } else {
          ZenSnackbar.show(
            context,
            orderState.message,
            type: ToastType.warning,
          );
        }
      } else if (orderState is OrderError) {
        ZenSnackbar.show(
          context,
          'Failed to execute buy order: ${orderState.message}',
          type: ToastType.error,
        );
      }

      BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
    } catch (e) {
      if (!context.mounted) return;
      debugPrint(e.toString());
      ZenSnackbar.show(
        context,
        'Failed to execute buy order: $e',
        type: ToastType.error,
      );
    }
    BlocProvider.of<OrdersBloc>(context).add(OrdersInitializeEvent());
  }

  Future<void> sell(
    BuildContext context,
    String stockName,
    double price,
    int zenId,
  ) async {
    debugPrint('🥸  Sell requested for $stockName at $price');
    try {
      final graphBloc = context.read<GraphBloc>();
      graphBloc.add(
        GraphSellRequested(stockName: stockName, price: price, zenId: zenId),
      );

      if (!context.mounted) return;
      debugPrint("before modal $stockName $price $zenId $graphBloc");

      // Show the order form and wait for it to complete
      await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        transitionAnimationController: _formSheetAnimeController,
        builder: (modalContext) {
          return SingleChildScrollView(
            reverse: true,
            child: OrderFormSheet(
              openOrderType: FormOpenOrderType.spider,
              spiderMetaData: SpiderOrderData(
                zenId: zenId,
                tradingSymbol: stockName,
                transactionType: "SELL",
              ),
            ),
          );
        },
      ).whenComplete(() {
        debugPrint("Sell Spider form closed");
      }).catchError((error) {
        debugPrint("Error in sell modal: $error");
      });

      debugPrint("Sell Order form closed");
      BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
      //Closing 2nd slide (Buy Sell)
      if (Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (!context.mounted) return;

      final ordersBloc = context.read<OrdersBloc>();

      /// ✅ Only proceed if order was actually initiated (i.e., state is OrderLoading)
      if (ordersBloc.state is! OrderLoading) {
        debugPrint("No order initiated for $stockName. Skipping stream wait.");
        return;
      }

      // Show a loading dialog loader
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularLoader(),
              SizedBox(width: 16),
              Text("Processing your order..."),
            ],
          ),
        ),
      );

      // Wait for the order state to change from loading
      await for (final orderState in ordersBloc.stream) {
        if (orderState is! OrderLoading) break;
      }

      if (!context.mounted) return;

      // Close the loading dialog if it was shown
      if (Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      // Show appropriate snackbar based on order state
      final orderState = ordersBloc.state;
      debugPrint("Sell ${orderState.toString()}");

      if (orderState is OrderPlaced) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Sell order executed for $stockName at ₹${price.toStringAsFixed(2)}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      } else if (orderState is OrderError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Failed to execute sell order: ${orderState.message}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      // Clear selected stock
      if (context.mounted) {
        BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
      }
    } catch (e) {
      if (!context.mounted) return;
      debugPrint(e.toString());
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to execute sell order: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // Resetting to initial event
    BlocProvider.of<OrdersBloc>(context).add(OrdersInitializeEvent());
  }

//this will be improved by using seperate bloc for orders prefrences overall
  Future<void> bulkOrderCallback(
    BuildContext context,
    List<IndexInvestment> indexInvestments,
  ) async {

    // Otherwise, show the dialog to ask for details
    BrokerAccountStrategySelectionBloc selectionBloC =
        BlocProvider.of<BrokerAccountStrategySelectionBloc>(context);
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) {
        return BlocProvider.value(
          value: selectionBloC,
          child: OrderPrefrenceModal(
            onDefaultSet: (clientId) async {},
          ),
        );
      },
    );

    if (result != null) {
      final authState = context.read<AuthBloc>().state;
      if (authState is! AuthAuthenticated) return;
      final credentialsModel = authState.credentialsModel;
      List<OrderFormModel> orders =
          OrderAdapterService.convertIndexInvestmentsToOrders(
        indexInvestments: indexInvestments,
        clientId: credentialsModel.clientId,
        accountId: result['accountId'],
        strategyId: result['strategyId'],
        broker: result['brokerName'],
        exchange: "NSE",
        transactionType: "BUY", // or "SELL"
      );
      BlocProvider.of<OrdersBloc>(context).add(PlaceBulkOrderEvent(orders));
      debugPrint(orders.toString());
    }
  }

  @override
  void dispose() {
    debugPrint('Disposing SpiderPage');
    _webSocketManager?.dispose();
    _webSocketManager = null;
    _repository?.dispose(); // Add dispose method to repository if needed
    _repository = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AuthState authState = context.read<AuthBloc>().state;
    if (authState is AuthUnauthenticated) {
      Navigator.pushReplacementNamed(context, '/login');
    }

    return BlocListener<OrdersBloc, OrdersState>(
      listener: (context, state) {
        if (state is BulkOrderProcessing) {
          // Just show loader if not already shown
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) => const Center(
              child: CircularLoader(),
            ),
          );
        } else if (state is BulkOrderCompleted) {
          // Close loader
          Navigator.of(context, rootNavigator: true).pop();

          ZenSnackbar.show(
            context,
            'Bulk Order Completed, ${state.accepted} accepted, ${state.rejected} rejected.',
            type: ToastType.info,
            duration: const Duration(seconds: 4),
          );
        } else if (state is BulkOrderError) {
          // Close loader
          Navigator.of(context, rootNavigator: true).pop();

          ZenSnackbar.show(
            context,
            'Bulk Order Failed: ${state.error}',
            type: ToastType.error,
          );
        }
      },
      child: Scaffold(
        backgroundColor: ThemeConstants.backgroundColor,
        body: MultiProvider(
          providers: [
            Provider<StockRepository>(
              create: (context) {
                _repository?.dispose();
                _repository = StockRepositoryImpl(
                  client: http.Client(),
                  accessToken: SharedPreferencesService.instance.accessToken,
                );
                _repository?.addPortfolioData(portfolioSpiderData!);

                _webSocketManager?.dispose();
                _webSocketManager = WebSocketManager(
                  webSocketBloc: context.read<WebSocketBloc>(),
                  onDataReceived: (data) {
                    if (mounted) {
                      _repository?.handleWebSocketData(data);
                    }
                  },
                );

                _repository?.setWebSocketHandler(
                  onDataReceived: (priceMap) {
                    if (mounted) {
                      // Check if widget is still mounted
                    }
                  },
                  onToggle: () {
                    _webSocketManager?.toggle();
                  },
                  getConnectionStatus: () =>
                      _webSocketManager?.isConnected ?? false,
                );

                if (mounted) {
                  Future.microtask(() {
                    _webSocketManager?.connect(
                        SharedPreferencesService.instance.accessToken ?? '');
                  });
                }

                return _repository!; // Will be cast to StockRepository
              },
              dispose: (_, repository) =>
                  (repository as StockRepositoryImpl).dispose(),
            ),
            BlocProvider<GraphBloc>(
              create: (context) => GraphBloc(
                stockRepository: context.read<StockRepository>(),
                webApiService: WebApiService(
                    accessToken: SharedPreferencesService.instance.accessToken),
              ),
            ),
            BlocProvider<BrokerAccountStrategySelectionBloc>(
              create: (context) => BrokerAccountStrategySelectionBloc(
                (authState as AuthAuthenticated).credentialsModel,
              ),
            ),
          ],
          child: SpiderGraph(
            onBuy: buy,
            onSell: sell,
            onInvestmentConfirmed: bulkOrderCallback,
          ),
        ),
      ),
    );
  }
}
